#property strict

//+------------------------------------------------------------------+
//| TradingMessageHandler class definition                           |
//| 專為 PipelineAdvance_v1 模組設計的統一訊息處理器                |
//+------------------------------------------------------------------+

// 防止重複包含
#ifndef _TRADING_ERROR_HANDLER_MQH_
#define _TRADING_ERROR_HANDLER_MQH_

// 引入必要的模組
#include "TradingEvent.mqh"

// 前向聲明
class PipelineResult;
class TradingMessageRecord;

//+------------------------------------------------------------------+
//| TradingMessageVisitor 介面 - 訊息處理訪問者模式                 |
//+------------------------------------------------------------------+
class TradingMessageVisitor
{
public:
    // 構造函數
    TradingMessageVisitor() {}

    // 析構函數
    virtual ~TradingMessageVisitor() {}

    // 訪問訊息記錄的方法，由子類實現
    virtual void Visit(const TradingMessageRecord& record) = 0;
};

//+------------------------------------------------------------------+
//| 訊息記錄項目類別                                                 |
//+------------------------------------------------------------------+
class TradingMessageRecord
{
public:
    string m_message;                    // 訊息內容
    string m_source;                     // 訊息來源
    ENUM_ERROR_LEVEL m_errorLevel;       // 訊息級別
    datetime m_timestamp;                // 時間戳

    // 構造函數
    TradingMessageRecord(string message = "", string source = "", ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
        : m_message(message), m_source(source), m_errorLevel(errorLevel), m_timestamp(TimeCurrent()) {}

    // 析構函數
    ~TradingMessageRecord() {}
};

//+------------------------------------------------------------------+
//| TradingMessageHandler 類別                                      |
//+------------------------------------------------------------------+
class TradingMessageHandler
{
private:
    string m_last_message;               // 最後一個訊息
    TradingMessageRecord* m_message_log[]; // 訊息記錄指針陣列
    int m_message_count;                 // 訊息計數
    int m_max_messages;                  // 最大訊息數量

    // 各級別訊息計數
    int m_info_count;                    // 信息級別計數
    int m_warning_count;                 // 警告級別計數
    int m_error_count;                   // 錯誤級別計數
    int m_critical_count;                // 嚴重級別計數

public:
    // 構造函數
    TradingMessageHandler()
    {
        m_last_message = "";
        m_message_count = 0;
        m_max_messages = 50;  // 默認最大訊息數量

        // 初始化各級別計數
        m_info_count = 0;
        m_warning_count = 0;
        m_error_count = 0;
        m_critical_count = 0;

        ArrayResize(m_message_log, m_max_messages);

        // 初始化指針陣列
        for(int i = 0; i < m_max_messages; i++)
        {
            m_message_log[i] = NULL;
        }
    }

    // 析構函數
    ~TradingMessageHandler()
    {
        // 清理所有訊息記錄對象
        for(int i = 0; i < m_message_count; i++)
        {
            if(m_message_log[i] != NULL)
            {
                delete m_message_log[i];
                m_message_log[i] = NULL;
            }
        }
        ArrayFree(m_message_log);
    }

    // 設置最大訊息數量
    void SetMaxMessages(int maxMsg)
    {
        if(maxMsg > 0)
        {
            // 如果新大小小於當前訊息數量，需要清理多餘的訊息記錄
            if(maxMsg < m_message_count)
            {
                for(int i = maxMsg; i < m_message_count; i++)
                {
                    if(m_message_log[i] != NULL)
                    {
                        // 更新級別計數
                        UpdateLevelCount(m_message_log[i].m_errorLevel, -1);
                        delete m_message_log[i];
                        m_message_log[i] = NULL;
                    }
                }
                m_message_count = maxMsg;
            }

            m_max_messages = maxMsg;
            ArrayResize(m_message_log, m_max_messages);

            // 初始化新增的指針位置
            for(int i = m_message_count; i < m_max_messages; i++)
            {
                m_message_log[i] = NULL;
            }
        }
    }

    // 添加訊息 - 無輸出版本
    void AddMessage(string message, string source = "", ENUM_ERROR_LEVEL level = ERROR_LEVEL_INFO)
    {
        m_last_message = message;

        // 創建訊息記錄
        TradingMessageRecord* record = new TradingMessageRecord(message, source, level);

        // 添加到訊息日誌
        AddMessageRecord(record);

        // 更新級別計數
        UpdateLevelCount(level, 1);
    }

    // 處理訊息 - 使用 visitor 模式
    void HandleMessage(TradingMessageVisitor* visitor)
    {
        if(visitor == NULL) return;

        // 遍歷所有訊息記錄，讓 visitor 處理
        for(int i = 0; i < m_message_count; i++)
        {
            if(m_message_log[i] != NULL)
            {
                visitor.Visit(*m_message_log[i]);
            }
        }
    }

    // 處理 PipelineResult 訊息 - 專門方法
    void HandlePipelineResult(PipelineResult* result);  // 實現將在包含 PipelineResult 定義後提供

    // 批量處理 PipelineResult 陣列
    void HandlePipelineResults(PipelineResult* &results[], int count);  // 實現將在包含 PipelineResult 定義後提供

    // 清除所有訊息
    void ClearMessages()
    {
        // 清理所有訊息記錄對象
        for(int i = 0; i < m_message_count; i++)
        {
            if(m_message_log[i] != NULL)
            {
                delete m_message_log[i];
                m_message_log[i] = NULL;
            }
        }

        m_message_count = 0;
        m_last_message = "";

        // 重置級別計數
        m_info_count = 0;
        m_warning_count = 0;
        m_error_count = 0;
        m_critical_count = 0;
    }

    // 獲取最後一個訊息（指定級別）
    string GetLastMessage(ENUM_ERROR_LEVEL level = ERROR_LEVEL_INFO) const
    {
        // 從後往前查找指定級別的最後一個訊息
        for(int i = m_message_count - 1; i >= 0; i--)
        {
            if(m_message_log[i] != NULL && m_message_log[i].m_errorLevel == level)
            {
                return m_message_log[i].m_message;
            }
        }
        return "";
    }

    // 獲取第一個訊息（指定級別）
    string GetFirstMessage(ENUM_ERROR_LEVEL level = ERROR_LEVEL_INFO) const
    {
        for(int i = 0; i < m_message_count; i++)
        {
            if(m_message_log[i] != NULL && m_message_log[i].m_errorLevel == level)
            {
                return m_message_log[i].m_message;
            }
        }
        return "";
    }

    // 獲取訊息計數
    int GetMessageCount() const
    {
        return m_message_count;
    }

    // 檢查是否為空
    bool IsEmpty() const
    {
        return m_message_count == 0;
    }

    // 獲取指定級別的訊息計數
    int GetMessageCountByLevel(ENUM_ERROR_LEVEL level) const
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return m_info_count;
            case ERROR_LEVEL_WARNING:  return m_warning_count;
            case ERROR_LEVEL_ERROR:    return m_error_count;
            case ERROR_LEVEL_CRITICAL: return m_critical_count;
            default:                   return 0;
        }
    }

    // 檢查是否有嚴重錯誤
    bool HasCriticalMessages() const
    {
        return m_critical_count > 0;
    }

    // 檢查是否有錯誤級別訊息
    bool HasErrorMessages() const
    {
        return m_error_count > 0;
    }

    // 檢查是否有警告級別訊息
    bool HasWarningMessages() const
    {
        return m_warning_count > 0;
    }

    // 獲取訊息記錄
    bool GetMessageRecord(int index, TradingMessageRecord& record) const
    {
        if(index >= 0 && index < m_message_count && m_message_log[index] != NULL)
        {
            record = *m_message_log[index];
            return true;
        }
        return false;
    }

    // 獲取所有訊息的摘要
    string GetMessageSummary() const
    {
        string summary = StringFormat("訊息總數: %d\n", m_message_count);
        summary += StringFormat("信息: %d, 警告: %d, 錯誤: %d, 嚴重: %d",
                               m_info_count,
                               m_warning_count,
                               m_error_count,
                               m_critical_count);
        return summary;
    }

    // 彈出最後一個訊息（移除並返回）
    string PopMessage(ENUM_ERROR_LEVEL level = ERROR_LEVEL_INFO)
    {
        if(m_message_count > 0)
        {
            int lastIndex = m_message_count - 1;
            string message = "";

            if(m_message_log[lastIndex] != NULL)
            {
                message = m_message_log[lastIndex].m_message;
                // 更新級別計數
                UpdateLevelCount(m_message_log[lastIndex].m_errorLevel, -1);
                delete m_message_log[lastIndex];
                m_message_log[lastIndex] = NULL;
            }

            m_message_count--;

            // 更新最後訊息
            if(m_message_count > 0 && m_message_log[m_message_count - 1] != NULL)
            {
                m_last_message = m_message_log[m_message_count - 1].m_message;
            }
            else
            {
                m_last_message = "";
            }

            return message;
        }
        return "";
    }

    // 移除第一個訊息（移除並返回）
    string ShiftMessage(ENUM_ERROR_LEVEL level = ERROR_LEVEL_INFO)
    {
        if(m_message_count > 0)
        {
            string message = "";

            if(m_message_log[0] != NULL)
            {
                message = m_message_log[0].m_message;
                // 更新級別計數
                UpdateLevelCount(m_message_log[0].m_errorLevel, -1);
                delete m_message_log[0];
            }

            // 向前移動所有訊息記錄
            for(int i = 1; i < m_message_count; i++)
            {
                m_message_log[i - 1] = m_message_log[i];
            }

            m_message_count--;
            m_message_log[m_message_count] = NULL;

            // 更新最後訊息
            if(m_message_count > 0 && m_message_log[m_message_count - 1] != NULL)
            {
                m_last_message = m_message_log[m_message_count - 1].m_message;
            }
            else
            {
                m_last_message = "";
            }

            return message;
        }
        return "";
    }

private:
    // 添加訊息記錄到陣列
    void AddMessageRecord(TradingMessageRecord* record)
    {
        if(m_message_count < m_max_messages)
        {
            m_message_log[m_message_count] = record;
            m_message_count++;
        }
        else
        {
            // 刪除最舊的訊息記錄
            if(m_message_log[0] != NULL)
            {
                // 更新級別計數
                UpdateLevelCount(m_message_log[0].m_errorLevel, -1);
                delete m_message_log[0];
            }

            // 移動陣列元素，為新訊息騰出空間
            for(int i = 1; i < m_message_count; i++)
            {
                m_message_log[i-1] = m_message_log[i];
            }
            m_message_log[m_max_messages - 1] = record;
        }
    }

    // 更新級別計數
    void UpdateLevelCount(ENUM_ERROR_LEVEL level, int delta)
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:
                m_info_count += delta;
                break;
            case ERROR_LEVEL_WARNING:
                m_warning_count += delta;
                break;
            case ERROR_LEVEL_ERROR:
                m_error_count += delta;
                break;
            case ERROR_LEVEL_CRITICAL:
                m_critical_count += delta;
                break;
        }
    }

    // 將訊息級別轉換為字符串
    string MessageLevelToString(ENUM_ERROR_LEVEL level) const
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return "INFO";
            case ERROR_LEVEL_WARNING:  return "WARNING";
            case ERROR_LEVEL_ERROR:    return "ERROR";
            case ERROR_LEVEL_CRITICAL: return "CRITICAL";
            default:                   return "UNKNOWN";
        }
    }
};



//+------------------------------------------------------------------+
//| PipelineResult 相關方法的實現                                    |
//| 注意：這些方法需要在包含 PipelineResult 定義後才能使用           |
//+------------------------------------------------------------------+

// 包含 PipelineResult 定義的宏
#ifdef PIPELINE_RESULT_DEFINED

// 處理 PipelineResult 訊息的實現
void TradingMessageHandler::HandlePipelineResult(PipelineResult* result)
{
    if(result == NULL) return;

    if(!result.IsSuccess())
    {
        AddMessage(result.GetMessage(), result.GetSource(), result.GetErrorLevel());
    }
}

// 批量處理 PipelineResult 陣列的實現
void TradingMessageHandler::HandlePipelineResults(PipelineResult* &results[], int count)
{
    for(int i = 0; i < count; i++)
    {
        if(results[i] != NULL)
        {
            HandlePipelineResult(results[i]);
        }
    }
}

#endif // PIPELINE_RESULT_DEFINED

//+------------------------------------------------------------------+
//| 具體的 TradingMessageVisitor 實現                               |
//+------------------------------------------------------------------+

// 日誌記錄 Visitor - 替代原來的 LogMessage 方法
class LogMessageVisitor : public TradingMessageVisitor
{
public:
    LogMessageVisitor() {}
    virtual ~LogMessageVisitor() {}

    virtual void Visit(const TradingMessageRecord& record) override
    {
        string levelStr = MessageLevelToString(record.m_errorLevel);
        string logMessage = StringFormat("[%s] %s: %s",
                                       levelStr,
                                       record.m_source,
                                       record.m_message);

        // 使用 Print 輸出訊息信息
        Print("TradingMessageHandler: ", logMessage);
    }

private:
    // 將訊息級別轉換為字符串
    string MessageLevelToString(ENUM_ERROR_LEVEL level) const
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return "INFO";
            case ERROR_LEVEL_WARNING:  return "WARNING";
            case ERROR_LEVEL_ERROR:    return "ERROR";
            case ERROR_LEVEL_CRITICAL: return "CRITICAL";
            default:                   return "UNKNOWN";
        }
    }
};

// 訊息統計 Visitor
class MessageStatisticsVisitor : public TradingMessageVisitor
{
private:
    int m_info_count;
    int m_warning_count;
    int m_error_count;
    int m_critical_count;

public:
    MessageStatisticsVisitor()
    {
        m_info_count = 0;
        m_warning_count = 0;
        m_error_count = 0;
        m_critical_count = 0;
    }

    virtual ~MessageStatisticsVisitor() {}

    virtual void Visit(const TradingMessageRecord& record) override
    {
        switch(record.m_errorLevel)
        {
            case ERROR_LEVEL_INFO:
                m_info_count++;
                break;
            case ERROR_LEVEL_WARNING:
                m_warning_count++;
                break;
            case ERROR_LEVEL_ERROR:
                m_error_count++;
                break;
            case ERROR_LEVEL_CRITICAL:
                m_critical_count++;
                break;
        }
    }

    // 獲取統計結果
    int GetInfoCount() const { return m_info_count; }
    int GetWarningCount() const { return m_warning_count; }
    int GetErrorCount() const { return m_error_count; }
    int GetCriticalCount() const { return m_critical_count; }
    int GetTotalCount() const { return m_info_count + m_warning_count + m_error_count + m_critical_count; }

    string GetSummary() const
    {
        return StringFormat("訊息統計 - 信息: %d, 警告: %d, 錯誤: %d, 嚴重: %d, 總計: %d",
                           m_info_count, m_warning_count, m_error_count, m_critical_count, GetTotalCount());
    }
};

// 訊息過濾 Visitor - 只處理特定級別的訊息
class MessageFilterVisitor : public TradingMessageVisitor
{
private:
    ENUM_ERROR_LEVEL m_filter_level;
    TradingMessageVisitor* m_target_visitor;

public:
    MessageFilterVisitor(ENUM_ERROR_LEVEL filterLevel, TradingMessageVisitor* targetVisitor)
        : m_filter_level(filterLevel), m_target_visitor(targetVisitor) {}

    virtual ~MessageFilterVisitor() {}

    virtual void Visit(const TradingMessageRecord& record) override
    {
        if(record.m_errorLevel == m_filter_level && m_target_visitor != NULL)
        {
            m_target_visitor.Visit(record);
        }
    }
};

// 結束防止重複包含
#endif // _TRADING_ERROR_HANDLER_MQH_
